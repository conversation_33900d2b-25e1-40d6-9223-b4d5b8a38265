

export interface UserDocument {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  category: string;
  file_path: string;
  file_name?: string;
  file_type?: string;
  file_size?: number;
  created_at: string;
  updated_at: string;
}

export interface Contact {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  name?: string; // Keeping for backward compatibility
  email?: string;
  phone?: string;
  country_code?: string;
  relationship: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface ServiceSunset {
  id: string;
  user_id: string;
  name: string;
  category: string;
  custom_category?: string;
  description?: string;
  website?: string;
  account_number?: string;
  contact_info?: string;
  cancellation_instructions?: string;
  priority: string;
  auto_renewal: boolean;
  renewal_date?: string;
  cost_per_period?: number;
  period?: string;
  created_at: string;
  updated_at: string;
}

export interface Trustee {
  id: string;
  user_id: string;           // The user who created the trustee (grantor)
  trustee_email: string;     // Email of the invited trustee
  trustee_user_id?: string;  // User ID of the trustee (once they accept)
  first_name: string;
  last_name: string;
  relationship: string;
  status: 'pending' | 'active' | 'revoked';
  permissions: string[];     // Array of permissions (assets, vault, contacts, etc.)
  invitation_sent_at: string;
  invitation_accepted_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DeathNotification {
  id: string;
  user_id: string;           // The deceased user
  reported_by: string;       // User ID of the trustee who reported the death
  status: 'pending' | 'verified' | 'rejected';
  verification_method?: string;
  verification_details?: string;
  death_certificate?: string; // Optional path to uploaded death certificate
  reported_at: string;
  verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface LastWishes {
  id: string;
  user_id: string;
  funeral_wishes?: string;    // Funeral and memorial preferences
  burial_wishes?: string;     // Burial or cremation preferences
  burial_option?: string;     // Selected burial option (burial, cremation, etc.)
  personal_messages?: string; // Personal messages to loved ones
  show_personal_messages?: boolean; // Whether to show personal messages section
  is_organ_donor: boolean;    // Whether the user is an organ donor
  organ_donor_country?: string; // Country where the user is registered as an organ donor
  organ_donor_state?: string; // State where the user is registered as an organ donor (for USA)
  has_pets: boolean;          // Whether the user has pets
  pet_care_instructions?: string; // Instructions for pet care
  other_wishes?: string;      // Any other final wishes
  created_at: string;
  updated_at: string;
}

export interface Asset {
  id: string;
  user_id: string;
  name: string;
  type: 'digital' | 'physical';
  category: string;
  description?: string;
  location?: string;
  value?: number;
  currency?: string;
  acquisition_date?: string | Date;
  account_details?: string;
  beneficiary?: string;
  notes?: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface TimeCapsule {
  id: string;
  user_id: string;
  title: string;
  message: string | null;
  recipient_name?: string;
  recipient_first_name?: string;
  recipient_last_name?: string;
  recipient_email: string;
  delivery_date: string;
  delivery_hour?: number;
  status: 'draft' | 'scheduled' | 'delivered' | 'cancelled';
  access_code: string;
  created_at: string;
  updated_at: string;
  sender_name?: string | null;
}

export interface TimeCapsuleMedia {
  id: string;
  capsule_id: string;
  media_type: string;
  file_path: string;
  file_name: string;
  file_type?: string;
  file_size: number;
  created_at: string;
  signed_url?: string;
}

export interface Database {
  public: {
    Tables: {
      vault_documents: {
        Row: UserDocument;
        Insert: Omit<UserDocument, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<UserDocument, 'id' | 'created_at' | 'updated_at'>>;
      };
      documents: {
        Row: UserDocument;
        Insert: Omit<UserDocument, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<UserDocument, 'id' | 'created_at' | 'updated_at'>>;
      };
      time_capsules: {
        Row: TimeCapsule;
        Insert: Omit<TimeCapsule, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<TimeCapsule, 'id' | 'created_at' | 'updated_at'>>;
      };
      time_capsule_media: {
        Row: TimeCapsuleMedia;
        Insert: Omit<TimeCapsuleMedia, 'id' | 'created_at'>;
        Update: Partial<Omit<TimeCapsuleMedia, 'id' | 'created_at'>>;
      };
      contacts: {
        Row: Contact;
        Insert: Omit<Contact, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Contact, 'id' | 'created_at' | 'updated_at'>>;
      };
      service_sunset: {
        Row: ServiceSunset;
        Insert: Omit<ServiceSunset, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<ServiceSunset, 'id' | 'created_at' | 'updated_at'>>;
      };
      assets: {
        Row: Asset;
        Insert: Omit<Asset, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Asset, 'id' | 'created_at' | 'updated_at'>>;
      };
      trustees: {
        Row: Trustee;
        Insert: Omit<Trustee, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<Trustee, 'id' | 'created_at' | 'updated_at'>>;
      };
      death_notifications: {
        Row: DeathNotification;
        Insert: Omit<DeathNotification, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<DeathNotification, 'id' | 'created_at' | 'updated_at'>>;
      };
      last_wishes: {
        Row: LastWishes;
        Insert: Omit<LastWishes, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<LastWishes, 'id' | 'created_at' | 'updated_at'>>;
      };
    };
    Views: {
      [_ in never]: never
    };
    Functions: {
      [_ in never]: never
    };
    Enums: {
      [_ in never]: never
    };
  };
}

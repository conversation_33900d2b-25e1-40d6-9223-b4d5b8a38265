"use client";

import React, { useEffect, useState } from 'react';
import { FileText, ArrowRight, PlusCircle, Crown } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';

interface DocumentCount {
  count: number;
  hasDocuments: boolean;
}

const VaultSummary = () => {
  const { user } = useAuth();
  const [documentInfo, setDocumentInfo] = useState<DocumentCount>({ count: 0, hasDocuments: false });
  const [userTier, setUserTier] = useState<'free' | 'premium'>('free');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDocumentInfo = async () => {
      if (!user) return;

      try {
        // Fetch documents
        const response = await fetch('/api/vault/documents');
        if (response.ok) {
          const documents = await response.json();
          setDocumentInfo({
            count: documents.length,
            hasDocuments: documents.length > 0
          });
        }

        // Fetch user subscription tier
        const profileResponse = await fetch('/api/profile');
        if (profileResponse.ok) {
          const profile = await profileResponse.json();
          setUserTier(profile.subscription_tier || 'free');
        }
      } catch (error) {
        console.error('Error fetching vault info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocumentInfo();
  }, [user]);

  // Show upgrade prompt for free tier users who have reached the limit
  const showUpgradePrompt = userTier === 'free' && documentInfo.count >= 1;

  if (isLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center">
            <div className="feature-card-icon bg-blue-100">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="feature-card-title">Digital Vault</h3>
              <p className="feature-card-subtitle">Loading...</p>
            </div>
          </div>
        </div>
        <div className="feature-card-empty bg-gray-50">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-gray-300 rounded mx-auto mb-2"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <FileText className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Digital Vault</h3>
            <p className="feature-card-subtitle">
              {documentInfo.hasDocuments
                ? `${documentInfo.count} document${documentInfo.count !== 1 ? 's' : ''}`
                : 'Store important documents'
              }
            </p>
          </div>
        </div>
      </div>

      {showUpgradePrompt ? (
        <div className="feature-card-empty bg-amber-50 border border-amber-200">
          <Crown className="feature-card-empty-icon text-amber-500" />
          <h4 className="feature-card-empty-title text-amber-900">Upgrade to Add More Documents</h4>
          <p className="feature-card-empty-description text-amber-700">
            You've reached the free tier limit of 1 document. Upgrade to Premium for unlimited documents and 5GB storage.
          </p>
          <Link
            href="/pricing"
            className="feature-card-empty-button bg-amber-600 hover:bg-amber-700"
          >
            <Crown className="mr-2 h-4 w-4" />
            Upgrade to Premium
          </Link>
        </div>
      ) : (
        <div className="feature-card-empty bg-blue-50">
          <FileText className="feature-card-empty-icon text-blue-500" />
          <h4 className="feature-card-empty-title text-blue-900">
            {documentInfo.hasDocuments ? 'Manage Documents' : 'Document Storage'}
          </h4>
          <p className="feature-card-empty-description text-blue-700">
            {documentInfo.hasDocuments
              ? 'View and manage your stored documents.'
              : 'Securely store important documents in your digital vault.'
            }
            {userTier === 'free' && !documentInfo.hasDocuments && (
              <span className="block text-xs mt-1">Free tier: 1 document allowed</span>
            )}
          </p>
          <Link
            href="/vault"
            className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            {documentInfo.hasDocuments ? 'Manage Documents' : 'Upload Document'}
          </Link>
        </div>
      )}
    </div>
  );
};

export default VaultSummary;

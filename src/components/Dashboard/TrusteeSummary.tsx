"use client";

import React, { useEffect, useState } from 'react';
import { Users, ArrowRight, PlusCircle, Crown } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/context/auth-context';

interface TrusteeInfo {
  count: number;
  hasTrustees: boolean;
}

const TrusteeSummary = () => {
  const { user } = useAuth();
  const [trusteeInfo, setTrusteeInfo] = useState<TrusteeInfo>({ count: 0, hasTrustees: false });
  const [userTier, setUserTier] = useState<'free' | 'premium'>('free');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchTrusteeInfo = async () => {
      if (!user) return;

      try {
        // Fetch trustees
        const response = await fetch('/api/trustees');
        if (response.ok) {
          const trustees = await response.json();
          setTrusteeInfo({
            count: trustees.length,
            hasTrustees: trustees.length > 0
          });
        }

        // Fetch user subscription tier
        const profileResponse = await fetch('/api/profile');
        if (profileResponse.ok) {
          const profile = await profileResponse.json();
          setUserTier(profile.subscription_tier || 'free');
        }
      } catch (error) {
        console.error('Error fetching trustee info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrusteeInfo();
  }, [user]);

  // Show upgrade prompt for free tier users who have reached the limit
  const showUpgradePrompt = userTier === 'free' && trusteeInfo.count >= 1;

  if (isLoading) {
    return (
      <div>
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center">
            <div className="feature-card-icon bg-blue-100">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="feature-card-title">Trustees</h3>
              <p className="feature-card-subtitle">Loading...</p>
            </div>
          </div>
        </div>
        <div className="feature-card-empty bg-gray-50">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-gray-300 rounded mx-auto mb-2"></div>
            <div className="h-4 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="feature-card-icon bg-blue-100">
            <Users className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="feature-card-title">Trustees</h3>
            <p className="feature-card-subtitle">
              {trusteeInfo.hasTrustees
                ? `${trusteeInfo.count} trustee${trusteeInfo.count !== 1 ? 's' : ''}`
                : 'Manage your trusted contacts'
              }
            </p>
          </div>
        </div>
      </div>

      {showUpgradePrompt ? (
        <div className="feature-card-empty bg-amber-50 border border-amber-200">
          <Crown className="feature-card-empty-icon text-amber-500" />
          <h4 className="feature-card-empty-title text-amber-900">Upgrade to Add More Trustees</h4>
          <p className="feature-card-empty-description text-amber-700">
            You've reached the free tier limit of 1 trustee. Upgrade to Premium for up to 5 trustees.
          </p>
          <Link
            href="/pricing"
            className="feature-card-empty-button bg-amber-600 hover:bg-amber-700"
          >
            <Crown className="mr-2 h-4 w-4" />
            Upgrade to Premium
          </Link>
        </div>
      ) : (
        <div className="feature-card-empty bg-blue-50">
          <Users className="feature-card-empty-icon text-blue-500" />
          <h4 className="feature-card-empty-title text-blue-900">
            {trusteeInfo.hasTrustees ? 'Manage Trustees' : 'Designate Trustees'}
          </h4>
          <p className="feature-card-empty-description text-blue-700">
            {trusteeInfo.hasTrustees
              ? 'View and manage your trusted individuals.'
              : 'Designate trusted individuals to handle your digital legacy.'
            }
            {userTier === 'free' && !trusteeInfo.hasTrustees && (
              <span className="block text-xs mt-1">Free tier: 1 trustee allowed</span>
            )}
          </p>
          <Link
            href="/trustees"
            className="feature-card-empty-button bg-blue-600 hover:bg-blue-700"
          >
            <Users className="mr-2 h-4 w-4" />
            {trusteeInfo.hasTrustees ? 'Manage Trustees' : 'Add Trustee'}
          </Link>
        </div>
      )}
    </div>
  );
};

export default TrusteeSummary;

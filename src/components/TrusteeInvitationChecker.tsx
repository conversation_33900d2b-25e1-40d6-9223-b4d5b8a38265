"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * This component checks for pending trustee invitations when a user logs in or signs up.
 * It should be included in the layout or on pages where users land after authentication.
 */
export default function TrusteeInvitationChecker() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    // Only run this check when a user is authenticated and not already checking
    if (loading || !user || isChecking) return;

    const checkForPendingInvitations = async () => {
      try {
        setIsChecking(true);
        console.log('Checking for pending trustee invitations for user:', user.id);

        // Check if there's a pending trustee ID in localStorage
        const pendingTrusteeId = localStorage.getItem('pendingTrusteeId');
        const pendingTrusteeEmail = localStorage.getItem('pendingTrusteeEmail');

        // First, check if there's a trustee ID in the URL (from redirect params)
        const urlParams = new URLSearchParams(window.location.search);
        const urlTrusteeId = urlParams.get('trusteeId');

        // Use the trustee ID from URL or localStorage
        const trusteeId = urlTrusteeId || pendingTrusteeId;

        if (trusteeId) {
          console.log('Found trustee invitation:', trusteeId, urlTrusteeId ? '(from URL)' : '(from localStorage)');
          console.log('Pending trustee email:', pendingTrusteeEmail);
          console.log('Current user email:', user.email);

          // Check if this trustee record exists and isn't already linked
          const { data: trusteeData, error: fetchError } = await supabase
            .from('trustees')
            .select('*')
            .eq('id', trusteeId)
            .single();

          if (fetchError) {
            console.error('Error fetching trustee record:', fetchError);
            // Clear invalid pending data
            localStorage.removeItem('pendingTrusteeId');
            localStorage.removeItem('pendingTrusteeEmail');
            localStorage.removeItem('pendingTrusteeAcceptedAt');
            return;
          }

          console.log('Trustee data found:', {
            id: trusteeData.id,
            email: trusteeData.trustee_email,
            status: trusteeData.status,
            currentUserId: trusteeData.trustee_user_id
          });

          // If the trustee is already linked to this user, we're done
          if (trusteeData.trustee_user_id === user.id) {
            console.log('Trustee already linked to current user');
            // Clear pending data
            localStorage.removeItem('pendingTrusteeId');
            localStorage.removeItem('pendingTrusteeEmail');
            localStorage.removeItem('pendingTrusteeAcceptedAt');
            return;
          }

          // If the trustee is linked to another user, show an error
          if (trusteeData.trustee_user_id && trusteeData.trustee_user_id !== user.id) {
            console.error('Trustee already linked to a different user:', trusteeData.trustee_user_id);
            toast.error('This invitation has already been accepted by another user');
            // Clear pending data
            localStorage.removeItem('pendingTrusteeId');
            localStorage.removeItem('pendingTrusteeEmail');
            localStorage.removeItem('pendingTrusteeAcceptedAt');
            return;
          }

          // Verify that the email matches if available
          if (pendingTrusteeEmail && user.email &&
              pendingTrusteeEmail.toLowerCase() !== user.email.toLowerCase() &&
              trusteeData.trustee_email.toLowerCase() !== user.email.toLowerCase()) {
            console.warn(`Email mismatch: Invitation for ${pendingTrusteeEmail}, but user logged in with ${user.email}`);
            // We'll still proceed, but log the mismatch
          }

          // Check if the user has accepted the terms
          const hasAcceptedTerms = localStorage.getItem('trusteeTermsAccepted') === 'true';
          if (!hasAcceptedTerms) {
            console.warn('User has not accepted trustee terms yet');
            // Don't proceed with activation if terms haven't been accepted
            return;
          }

          // Link the trustee to the user's account
          console.log('Linking trustee to user account:', user.id);

          // Use API route to update trustee record with admin privileges
          const response = await fetch('/api/trustees/update-status', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              trusteeId,
              trustee_user_id: user.id,
              status: 'active',
              invitation_accepted_at: new Date().toISOString(),
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            console.error('Error updating trustee record:', errorData);
            toast.error('Failed to complete trustee invitation');
            return;
          }

          // Double-check that the update was successful
          const { data: updatedTrustee, error: verifyError } = await supabase
            .from('trustees')
            .select('trustee_user_id, status, user_id')
            .eq('id', trusteeId)
            .single();

          if (verifyError) {
            console.error('Error verifying trustee update:', verifyError);
          } else {
            console.log('Verified trustee update:', {
              trusteeId: trusteeId,
              linkedUserId: updatedTrustee.trustee_user_id,
              inviterId: updatedTrustee.user_id,
              status: updatedTrustee.status
            });
          }

          console.log('Successfully linked trustee to user account');
          toast.success('You are now a trustee!');

          // Clear pending data
          localStorage.removeItem('pendingTrusteeId');
          localStorage.removeItem('pendingTrusteeEmail');
          localStorage.removeItem('pendingTrusteeAcceptedAt');
          localStorage.removeItem('trusteeTermsAccepted');

          // Check if we're already on the dashboard or completion page
          if (!window.location.pathname.includes('/dashboard') &&
              !window.location.pathname.includes('/trustee/complete')) {
            // Redirect to the dashboard
            router.push('/dashboard');
          }
        } else {
          console.log('No pending trustee invitation found in localStorage');
        }
      } catch (error) {
        console.error('Error checking for pending trustee invitations:', error);
      } finally {
        setIsChecking(false);
      }
    };

    checkForPendingInvitations();
  }, [user, loading, router, isChecking]);

  // This component doesn't render anything
  return null;
}

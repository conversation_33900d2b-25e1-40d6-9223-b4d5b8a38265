import { useState, useEffect } from 'react';

type PasswordStrength = 'weak' | 'medium' | 'strong';

export function PasswordStrengthIndicator({ password }: { password: string }) {
  const [strength, setStrength] = useState<PasswordStrength>('weak');
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (!password) {
      setStrength('weak');
      setMessage('');
      return;
    }

    // Calculate strength
    let score = 0;
    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChars = /[^A-Za-z0-9]/.test(password);
    const isLongEnough = password.length >= 8;

    if (hasLowercase) score++;
    if (hasUppercase) score++;
    if (hasNumbers) score++;
    if (hasSpecialChars) score++;
    if (isLongEnough) score += 2; // Extra points for length

    // Determine strength level
    let newStrength: PasswordStrength = 'weak';
    let newMessage = '';

    if (score >= 5) {
      newStrength = 'strong';
      newMessage = 'Strong password';
    } else if (score >= 3) {
      newStrength = 'medium';
      newMessage = 'Medium strength - add more complexity';
    } else {
      newMessage = 'Weak password - use more character types';
    }

    setStrength(newStrength);
    setMessage(newMessage);
  }, [password]);

  return (
    <div className="mt-2">
      <div className="flex gap-1">
        {[1, 2, 3].map((i) => (
          <div
            key={i}
            className={`h-1 flex-1 rounded-full ${
              (strength === 'strong' && i <= 3) ||
              (strength === 'medium' && i <= 2) ||
              (strength === 'weak' && i <= 1)
                ? strength === 'strong'
                  ? 'bg-green-500'
                  : strength === 'medium'
                  ? 'bg-yellow-500'
                  : 'bg-red-500'
                : 'bg-gray-200'
            }`}
          />
        ))}
      </div>
      {message && (
        <p
          className={`mt-1 text-xs ${
            strength === 'strong'
              ? 'text-green-600'
              : strength === 'medium'
              ? 'text-yellow-600'
              : 'text-red-600'
          }`}
        >
          {message}
        </p>
      )}
    </div>
  );
}

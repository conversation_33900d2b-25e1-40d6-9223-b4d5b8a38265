"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const serviceSunsetSchema = z.object({
  name: z.string().min(2, { message: 'Service name must be at least 2 characters' }),
  category: z.string().min(1, { message: 'Please select a category' }),
  custom_category: z.string().optional(),
  description: z.string().optional(),
  website: z.string().optional(),
  account_number: z.string().optional(),
  contact_info: z.string().optional(),
  cancellation_instructions: z.string().optional()
}).refine((data) => {
  // If category is 'other', custom_category must be provided
  return data.category !== 'other' || (data.custom_category && data.custom_category.trim().length > 0);
}, {
  message: "Please specify a custom category",
  path: ["custom_category"],
});

type ServiceSunsetFormValues = z.infer<typeof serviceSunsetSchema>;

interface ServiceSunsetFormProps {
  onSubmit: (data: ServiceSunsetFormValues) => void;
  onCancel?: () => void;
  defaultValues?: Partial<ServiceSunsetFormValues>;
}

const SERVICE_CATEGORIES = [
  { value: 'utilities', label: 'Utilities' },
  { value: 'internet', label: 'Internet' },
  { value: 'streaming', label: 'Streaming Services' },
  { value: 'subscription', label: 'Subscription' },
  { value: 'membership', label: 'Membership' },
  { value: 'financial', label: 'Financial' },
  { value: 'insurance', label: 'Insurance' },
  { value: 'telecom', label: 'Telecom' },
  { value: 'other', label: 'Other' },
];

export default function ServiceSunsetForm({ onSubmit, onCancel, defaultValues }: ServiceSunsetFormProps) {
  const form = useForm<ServiceSunsetFormValues>({
    resolver: zodResolver(serviceSunsetSchema),
    defaultValues: {
      name: defaultValues?.name || '',
      category: defaultValues?.category || '',
      custom_category: defaultValues?.custom_category || '',
      description: defaultValues?.description || '',
      website: defaultValues?.website || '',
      account_number: defaultValues?.account_number || '',
      contact_info: defaultValues?.contact_info || '',
      cancellation_instructions: defaultValues?.cancellation_instructions || ''
    },
  });

  const handleSubmit = (data: ServiceSunsetFormValues) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Service Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Service Name*</FormLabel>
                <FormControl>
                  <Input placeholder="Enter service name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Category */}
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category*</FormLabel>
                <Select onValueChange={(value) => {
                  field.onChange(value);
                  // Reset custom category when a different category is selected
                  if (value !== 'other') {
                    form.setValue('custom_category', '');
                  }
                }} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {SERVICE_CATEGORIES.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Custom Category - only shown when 'other' is selected */}
          {form.watch('category') === 'other' && (
            <FormField
              control={form.control}
              name="custom_category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Specify Category*</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter custom category" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Website */}
          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input placeholder="Service website URL" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Account Number */}
          <FormField
            control={form.control}
            name="account_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Account Number</FormLabel>
                <FormControl>
                  <Input placeholder="Account number or ID" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Contact Info */}
          <FormField
            control={form.control}
            name="contact_info"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Information</FormLabel>
                <FormControl>
                  <Input placeholder="Customer service phone or email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Cancellation Instructions */}
          <FormField
            control={form.control}
            name="cancellation_instructions"
            render={({ field }) => (
              <FormItem className="md:col-span-2">
                <FormLabel>Cancellation Instructions</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Detailed instructions for cancellation"
                    className="resize-none h-20"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit">
            Save Service
          </Button>
        </div>
      </form>
    </Form>
  );
}

"use client";

import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { UserDocument } from '@/types/database.types';
import { formatFileSize } from '@/lib/utils';
import { getSubscriptionLimits, SubscriptionTier } from '@/config/subscription-limits';
import { Crown } from 'lucide-react';
import Link from 'next/link';

interface VaultStatsProps {
  documents: UserDocument[];
}

export default function VaultStats({ documents }: VaultStatsProps) {
  const [userTier, setUserTier] = useState<SubscriptionTier>('free');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchUserTier = async () => {
      try {
        const response = await fetch('/api/profile');
        if (response.ok) {
          const profile = await response.json();
          setUserTier(profile.subscription_tier || 'free');
        }
      } catch (error) {
        console.error('Error fetching user tier:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserTier();
  }, []);

  const limits = getSubscriptionLimits(userTier);
  const totalSize = documents.reduce((sum, doc) => sum + (doc.file_size || 0), 0);
  const storageLimit = limits.maxVaultStorageGB * 1024 * 1024 * 1024; // Convert GB to bytes
  const usagePercent = Math.min(100, (totalSize / storageLimit) * 100);

  const categoryCounts = documents.reduce((acc, doc) => {
    const category = doc.category || 'other';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const documentCount = documents.length;
  const maxDocuments = limits.maxVaultDocuments;
  const documentUsagePercent = maxDocuments === Infinity ? 0 : Math.min(100, (documentCount / maxDocuments) * 100);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Vault Overview</h3>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-2 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-4">Vault Overview</h3>

        <div className="space-y-4">
          {/* Document Count (for free tier) */}
          {userTier === 'free' && (
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Documents</span>
                <span>{documentCount} / {maxDocuments}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className={`h-2.5 rounded-full ${documentCount >= maxDocuments ? 'bg-red-500' : 'bg-blue-600'}`}
                  style={{ width: `${documentUsagePercent}%` }}
                ></div>
              </div>
              {documentCount >= maxDocuments && (
                <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded-md">
                  <div className="flex items-center text-xs text-amber-700">
                    <Crown className="h-3 w-3 mr-1" />
                    <span>Document limit reached. </span>
                    <Link href="/pricing" className="underline ml-1">Upgrade to Premium</Link>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Storage Usage (for premium tier or info display) */}
          {userTier === 'premium' && (
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Storage Used</span>
                <span>{formatFileSize(totalSize)} / {formatFileSize(storageLimit)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${usagePercent}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Plan Info */}
          <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
            <div className="flex justify-between">
              <span>Plan:</span>
              <span className="capitalize font-medium">{userTier}</span>
            </div>
            {userTier === 'free' ? (
              <div className="mt-1 text-amber-600">
                Free tier: 1 document limit
              </div>
            ) : (
              <div className="mt-1 text-blue-600">
                Premium: Unlimited documents, 5GB storage
              </div>
            )}
          </div>

          {documents.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Documents by Category</h4>
              <div className="space-y-2">
                {Object.entries(categoryCounts).map(([category, count]) => (
                  <div key={category} className="flex justify-between text-sm">
                    <span className="capitalize">{category}</span>
                    <span>{count}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

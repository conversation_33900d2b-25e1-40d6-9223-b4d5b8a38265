"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/integrations/supabase/client';
import {
  Users,
  ArrowLeft,
  PlusCircle,
  Mail,
  Pencil,
  Trash2,
  AlertTriangle,
  Crown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import PageHeading from '@/components/ui/PageHeading';
import { toast } from 'sonner';
import TrusteeInviteForm from '@/components/Trustees/TrusteeInviteForm';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  Al<PERSON><PERSON><PERSON>og<PERSON><PERSON>er,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from '@/components/ui/badge';
import { getSubscriptionLimits, SubscriptionTier } from '@/config/subscription-limits';

type Trustee = {
  id: string;
  first_name: string;
  last_name: string;
  trustee_email: string;
  permissions: string[];
  status: string;
  invitation_sent_at: string | null;
};

export default function TrusteesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [trustees, setTrustees] = useState<Trustee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTrustee, setSelectedTrustee] = useState<Trustee | null>(null);
  const [isSendingInvite, setIsSendingInvite] = useState(false);
  const [userTier, setUserTier] = useState<SubscriptionTier>('free');

  useEffect(() => {
    if (user) {
      fetchTrustees();
      fetchUserTier();
    }
  }, [user]);

  const fetchUserTier = async () => {
    try {
      const response = await fetch('/api/profile');
      if (response.ok) {
        const profile = await response.json();
        setUserTier(profile.subscription_tier || 'free');
      }
    } catch (error) {
      console.error('Error fetching user tier:', error);
    }
  };

  const fetchTrustees = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/trustees');

      if (!response.ok) {
        throw new Error('Failed to fetch trustees');
      }

      const data = await response.json();
      setTrustees(data);
    } catch (error) {
      console.error('Error fetching trustees:', error);
      toast.error('Failed to load trustees');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTrustee = () => {
    setIsFormOpen(true);
  };

  const handleAddTrustee = async (data: any) => {
    try {
      const response = await fetch('/api/trustees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          first_name: data.firstName,
          last_name: data.lastName,
          email: data.email,
          permissions: data.permissions,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle upgrade prompts for subscription limits
        if (errorData.upgrade) {
          toast.error(errorData.error, {
            action: {
              label: 'Upgrade',
              onClick: () => window.open('/pricing', '_blank')
            }
          });
          return;
        }

        throw new Error(errorData.error || 'Failed to add trustee');
      }

      toast.success('Trustee added successfully');
      setIsFormOpen(false);
      fetchTrustees();
    } catch (error: any) {
      console.error('Error adding trustee:', error);
      toast.error(error.message || 'Error adding trustee');
    }
  };

  const handleDeleteTrustee = (trustee: Trustee) => {
    setSelectedTrustee(trustee);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteTrustee = async () => {
    if (!selectedTrustee) return;

    try {
      const response = await fetch(`/api/trustees?id=${selectedTrustee.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete trustee');
      }

      toast.success('Trustee deleted successfully');
      fetchTrustees();
    } catch (error: any) {
      console.error('Error deleting trustee:', error);
      toast.error(error.message || 'Error deleting trustee');
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedTrustee(null);
    }
  };

  const handleSendInvitation = async (trustee: Trustee) => {
    try {
      setIsSendingInvite(true);
      setSelectedTrustee(trustee);

      // Use the user object from auth context instead of querying the database
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Extract user information from the auth context
      const inviterName = user.firstName && user.lastName
        ? `${user.firstName} ${user.lastName}`
        : user.email?.split('@')[0] || 'User';
      const inviterEmail = user.email || '';
      const trusteeName = `${trustee.first_name} ${trustee.last_name}`;

      const response = await fetch('/api/send-trustee-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inviterName,
          inviterEmail,
          trusteeName,
          trusteeEmail: trustee.trustee_email,
          permissions: trustee.permissions,
          inviteId: trustee.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send invitation');
      }

      toast.success(`Invitation sent to ${trustee.trustee_email}`);
      fetchTrustees(); // Refresh to update the invitation_sent_at status
    } catch (error: any) {
      console.error('Error sending invitation:', error);
      toast.error(error.message || 'Error sending invitation');
    } finally {
      setIsSendingInvite(false);
      setSelectedTrustee(null);
    }
  };

  const getPermissionLabel = (permission: string) => {
    const labels: Record<string, string> = {
      'assets': 'Assets',
      'vault': 'Digital Vault',
      'contacts': 'Contacts',
      'services': 'Services',
      'last-wishes': 'Last Wishes'
    };
    return labels[permission] || permission;
  };

  const getStatusBadge = (trustee: Trustee) => {
    if (trustee.status === 'active') {
      return <Badge className="bg-green-500">Accepted</Badge>;
    } else if (trustee.status === 'pending_auth') {
      return <Badge className="bg-blue-500">Pending Signup</Badge>;
    } else if (trustee.status === 'pending' && trustee.invitation_sent_at) {
      return <Badge className="bg-yellow-500">Invited</Badge>;
    } else {
      return <Badge className="bg-gray-500">Not Invited</Badge>;
    }
  };

  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <PageHeading
        title="Trustees"
        description="Manage the people who will have access to your digital legacy after your passing."
        actions={
          trustees.length > 0 && (
            <Button onClick={handleCreateTrustee}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Trustee
            </Button>
          )
        }
      />

      <div className="mt-6">
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4">
              Your Trustees
              <span className="ml-2 text-sm font-normal text-gray-500">
                ({trustees.length} {trustees.length === 1 ? 'person' : 'people'})
              </span>
            </h3>
            {isLoading ? (
              // Loading skeletons
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-1/3" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                    <Skeleton className="h-8 w-16" />
                  </div>
                ))}
              </div>
            ) : trustees.length === 0 ? (
              // Empty state
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h4 className="mt-4 text-lg font-medium text-gray-900">No Trustees Yet</h4>
                <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                  Add people you trust to manage your digital legacy after your passing.
                  {userTier === 'free' && (
                    <span className="block text-xs mt-1 text-amber-600">Free tier: 1 trustee allowed</span>
                  )}
                </p>
                <Button onClick={handleCreateTrustee} className="mt-4">
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Your First Trustee
                </Button>
              </div>
            ) : (
              // Trustee list
              <div className="space-y-4">
                {trustees.map((trustee) => (
                  <div key={trustee.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="bg-blue-100 rounded-full p-2">
                        <Users className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {trustee.first_name} {trustee.last_name}
                        </h4>
                        <p className="text-sm text-gray-500">{trustee.trustee_email}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {trustee.permissions.map((permission) => (
                            <span key={permission} className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                              {getPermissionLabel(permission)}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(trustee)}

                      {trustee.status !== 'active' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSendInvitation(trustee)}
                          disabled={isSendingInvite && selectedTrustee?.id === trustee.id}
                        >
                          {isSendingInvite && selectedTrustee?.id === trustee.id ? (
                            <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full mr-2"></div>
                          ) : (
                            <Mail className="h-4 w-4 mr-2" />
                          )}
                          {trustee.invitation_sent_at ? 'Resend Invite' : 'Send Invite'}
                        </Button>
                      )}

                      <Button variant="ghost" size="sm" onClick={() => handleDeleteTrustee(trustee)}>
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Upgrade prompt for free tier users who have reached the limit */}
            {userTier === 'free' && trustees.length >= getSubscriptionLimits(userTier).maxTrustees && (
              <div className="mt-4 bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-center">
                  <Crown className="h-5 w-5 text-amber-500 mr-2" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-amber-900">Upgrade to Add More Trustees</h4>
                    <p className="text-xs text-amber-700 mt-1">
                      You've reached the free tier limit of 1 trustee. Upgrade to Premium for up to 5 trustees.
                    </p>
                  </div>
                  <Link href="/pricing">
                    <Button size="sm" className="bg-amber-600 hover:bg-amber-700">
                      <Crown className="mr-1 h-3 w-3" />
                      Upgrade
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

    {/* Trustee Form Dialog */}
    <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Trustee</DialogTitle>
          <DialogDescription>
            Add someone you trust to manage your digital legacy after your passing.
          </DialogDescription>
        </DialogHeader>
        <TrusteeInviteForm
          onSuccess={() => {
            setIsFormOpen(false);
            fetchTrustees();
          }}
          onCancel={() => setIsFormOpen(false)}
        />
      </DialogContent>
    </Dialog>

    {/* Delete Confirmation Dialog */}
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently remove {selectedTrustee?.first_name} {selectedTrustee?.last_name} from your trustees.
            This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={confirmDeleteTrustee} className="bg-red-600 hover:bg-red-700">
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
  );
}

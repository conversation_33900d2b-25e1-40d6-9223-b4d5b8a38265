import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    const { trusteeId, status, invitation_accepted_at, trustee_user_id } = await request.json();

    // Validate required fields
    if (!trusteeId || !status) {
      return NextResponse.json(
        { error: 'Missing required fields: trusteeId and status' },
        { status: 400 }
      );
    }

    console.log('Updating trustee status:', { trusteeId, status, invitation_accepted_at, trustee_user_id });

    // Prepare the update data
    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    };

    if (invitation_accepted_at) {
      updateData.invitation_accepted_at = invitation_accepted_at;
    }

    if (trustee_user_id) {
      updateData.trustee_user_id = trustee_user_id;
    }

    // Update the trustee record using admin client
    const { data, error } = await supabaseAdmin
      .from('trustees')
      .update(updateData)
      .eq('id', trusteeId)
      .select()
      .single();

    if (error) {
      console.error('Error updating trustee status:', error);
      return NextResponse.json(
        { error: error.message || 'Failed to update trustee status' },
        { status: 500 }
      );
    }

    console.log('Successfully updated trustee status:', data);

    return NextResponse.json({
      success: true,
      trustee: data
    });
  } catch (error: any) {
    console.error('Error in update trustee status API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { supabaseAdmin } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get pending trustee invitations for this user's email
    const { data: pendingInvitations, error } = await supabaseAdmin
      .from('trustees')
      .select(`
        id,
        user_id,
        trustee_email,
        first_name,
        last_name,
        relationship,
        status,
        permissions,
        invitation_sent_at,
        created_at,
        inviter:profiles!user_id (
          id,
          first_name,
          last_name,
          email
        )
      `)
      .eq('trustee_email', user.email)
      .in('status', ['pending', 'pending_auth'])
      .is('trustee_user_id', null);

    if (error) {
      console.error('Error fetching pending invitations:', error);
      return NextResponse.json(
        { error: 'Failed to fetch pending invitations' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      pendingInvitations: pendingInvitations || []
    });
  } catch (error: any) {
    console.error('Error in pending invitations API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { invitationId } = await request.json();

    if (!invitationId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      );
    }

    // Get the session token from cookies
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify the invitation exists and belongs to this user's email
    const { data: invitation, error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('*')
      .eq('id', invitationId)
      .eq('trustee_email', user.email)
      .single();

    if (fetchError || !invitation) {
      return NextResponse.json(
        { error: 'Invitation not found or not authorized' },
        { status: 404 }
      );
    }

    if (invitation.trustee_user_id) {
      return NextResponse.json(
        { error: 'Invitation has already been accepted' },
        { status: 400 }
      );
    }

    // Accept the invitation by linking it to the user
    const { error: updateError } = await supabaseAdmin
      .from('trustees')
      .update({
        trustee_user_id: user.id,
        status: 'active',
        invitation_accepted_at: new Date().toISOString(),
      })
      .eq('id', invitationId);

    if (updateError) {
      console.error('Error accepting invitation:', updateError);
      return NextResponse.json(
        { error: 'Failed to accept invitation' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Invitation accepted successfully'
    });
  } catch (error: any) {
    console.error('Error in accept invitation API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

"use client";

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/auth-context';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, RefreshCw } from 'lucide-react';

export default function DebugPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [sessionData, setSessionData] = useState<any>(null);
  const [profileData, setProfileData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        // Get session directly from Supabase
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          throw new Error(`Session error: ${sessionError.message}`);
        }

        setSessionData(session);

        // If we have a user, fetch their profile
        if (session?.user) {
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (profileError && profileError.code !== 'PGRST116') {
            console.error('Error fetching profile:', profileError);
            setError(`Profile error: ${profileError.message}`);
          }

          setProfileData(profile);
        }
      } catch (err) {
        console.error('Debug page error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Link
            href="/"
            className="flex items-center px-4 py-2 rounded-md bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-primary hover:border-primary transition-all shadow-sm w-fit"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
        </div>

        <h1 className="text-3xl font-bold mb-8">Authentication Debug Page</h1>

        {loading ? (
          <div className="bg-white p-8 rounded-xl shadow-md">
            <p className="text-gray-500">Loading authentication data...</p>
          </div>
        ) : error ? (
          <div className="bg-red-50 p-8 rounded-xl shadow-md border border-red-200">
            <h2 className="text-xl font-bold text-red-700 mb-4">Error</h2>
            <p className="text-red-600">{error}</p>
          </div>
        ) : (
          <div className="space-y-8">
            <div className="bg-white p-8 rounded-xl shadow-md">
              <h2 className="text-xl font-bold mb-4">Auth Context</h2>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold">User:</h3>
                  <pre className="bg-gray-100 p-4 rounded-md overflow-auto max-h-60 text-sm">
                    {user ? JSON.stringify(user, null, 2) : 'No user in context'}
                  </pre>
                </div>
                <div>
                  <h3 className="font-semibold">Session:</h3>
                  <pre className="bg-gray-100 p-4 rounded-md overflow-auto max-h-60 text-sm">
                    {sessionData ? JSON.stringify(sessionData, null, 2) : 'No session in context'}
                  </pre>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md">
              <h2 className="text-xl font-bold mb-4">Direct Supabase Data</h2>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold">Session:</h3>
                  <pre className="bg-gray-100 p-4 rounded-md overflow-auto max-h-60 text-sm">
                    {sessionData ? JSON.stringify(sessionData, null, 2) : 'No session from Supabase'}
                  </pre>
                </div>
                <div>
                  <h3 className="font-semibold">Profile:</h3>
                  <pre className="bg-gray-100 p-4 rounded-md overflow-auto max-h-60 text-sm">
                    {profileData ? JSON.stringify(profileData, null, 2) : 'No profile found'}
                  </pre>
                </div>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md">
              <h2 className="text-xl font-bold mb-4">Actions</h2>
              <div className="space-y-4">
                <div>
                  <Link href="/dashboard">
                    <Button>Go to Dashboard</Button>
                  </Link>
                </div>
                <div>
                  <Link href="/login">
                    <Button variant="outline">Go to Login</Button>
                  </Link>
                </div>
                <div className="pt-4 border-t border-gray-200 mt-4">
                  <h3 className="font-semibold mb-2">Manual Redirect</h3>
                  <Button
                    variant="secondary"
                    onClick={() => {
                      setRedirecting(true);
                      // Force a client-side redirect to dashboard
                      setTimeout(() => {
                        window.location.href = '/dashboard';
                      }, 500);
                    }}
                    disabled={redirecting}
                  >
                    {redirecting ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Redirecting...
                      </>
                    ) : (
                      'Force Redirect to Dashboard'
                    )}
                  </Button>
                </div>
                <div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      window.location.reload();
                    }}
                  >
                    Refresh Page
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

"use client";

import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/auth-context';
import { Button } from '@/components/ui/button';
import MainLayout from '@/components/Layout/MainLayout';

export default function AuthDebugPage() {
  const { user } = useAuth();
  const [supabaseSession, setSupabaseSession] = useState<any>(null);
  const [supabaseUser, setSupabaseUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function checkSupabaseAuth() {
      try {
        setLoading(true);
        setError(null);

        // Get session from Supabase
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          throw new Error(`Supabase session error: ${sessionError.message}`);
        }

        setSupabaseSession(session);
        setSupabaseUser(session?.user || null);
      } catch (err: any) {
        console.error('Auth debug error:', err);
        setError(err.message || 'An error occurred checking authentication');
      } finally {
        setLoading(false);
      }
    }

    checkSupabaseAuth();
  }, []);

  const refreshSession = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: refreshError } = await supabase.auth.refreshSession();

      if (refreshError) {
        throw new Error(`Failed to refresh session: ${refreshError.message}`);
      }

      setSupabaseSession(data.session);
      setSupabaseUser(data.user);

      alert('Session refreshed successfully');
    } catch (err: any) {
      console.error('Session refresh error:', err);
      setError(err.message || 'Failed to refresh session');
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>

        {loading ? (
          <p>Loading authentication data...</p>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <p className="text-red-600">{error}</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-semibold mb-4">Custom Auth Context</h2>
                {user ? (
                  <div>
                    <p><strong>User ID:</strong> {user.id}</p>
                    <p><strong>Email:</strong> {user.email}</p>
                    <p><strong>Name:</strong> {user.firstName} {user.lastName}</p>
                    <p><strong>Email Verified:</strong> {user.emailVerified ? 'Yes' : 'No'}</p>
                  </div>
                ) : (
                  <p className="text-amber-600">Not authenticated with custom auth</p>
                )}
              </div>

              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-semibold mb-4">Supabase Auth</h2>
                {supabaseUser ? (
                  <div>
                    <p><strong>User ID:</strong> {supabaseUser.id}</p>
                    <p><strong>Email:</strong> {supabaseUser.email}</p>
                    <p><strong>Created At:</strong> {new Date(supabaseUser.created_at).toLocaleString()}</p>
                    <p><strong>Last Sign In:</strong> {supabaseUser.last_sign_in_at ? new Date(supabaseUser.last_sign_in_at).toLocaleString() : 'N/A'}</p>
                  </div>
                ) : (
                  <p className="text-amber-600">Not authenticated with Supabase</p>
                )}
              </div>
            </div>

            {supabaseSession && (
              <div className="bg-white shadow rounded-lg p-6 mb-6">
                <h2 className="text-lg font-semibold mb-4">Supabase Session Details</h2>
                <p><strong>Access Token:</strong> {supabaseSession.access_token ? `${supabaseSession.access_token.substring(0, 20)}...` : 'None'}</p>
                <p><strong>Refresh Token:</strong> {supabaseSession.refresh_token ? `${supabaseSession.refresh_token.substring(0, 10)}...` : 'None'}</p>
                <p><strong>Expires At:</strong> {supabaseSession.expires_at ? new Date(supabaseSession.expires_at * 1000).toLocaleString() : 'N/A'}</p>
              </div>
            )}

            <div className="flex gap-4">
              <Button onClick={refreshSession} disabled={loading}>
                Refresh Supabase Session
              </Button>
            </div>
          </>
        )}
      </div>
    </MainLayout>
  );
}

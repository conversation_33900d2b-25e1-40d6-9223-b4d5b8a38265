"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/auth-context';
import { Button } from '@/components/ui/button';
import MainLayout from '@/components/Layout/MainLayout';
import { toast } from 'sonner';

export default function AuthFixPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const fixSupabaseAuth = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Check if we have a custom auth user but no Supabase session
      if (!user) {
        setError('You need to be logged in to fix authentication issues.');
        return;
      }

      // Try to sign in with Supa<PERSON> using email/password
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: prompt('Please enter your password to fix authentication:') || '',
      });

      if (signInError) {
        throw new Error(`Authentication failed: ${signInError.message}`);
      }

      if (data.session) {
        setSuccess('Authentication fixed successfully! Your session has been restored.');
        toast.success('Authentication fixed successfully!');
        
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        throw new Error('Failed to create a new session.');
      }
    } catch (err: any) {
      console.error('Auth fix error:', err);
      setError(err.message || 'An error occurred while fixing authentication');
      toast.error('Failed to fix authentication');
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Fix Authentication Issues</h1>
        
        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">Authentication Problem Detected</h2>
          <p className="mb-4">
            We've detected an issue with your authentication that's causing API errors. This can happen when:
          </p>
          <ul className="list-disc pl-6 mb-4 space-y-2">
            <li>Your session has expired</li>
            <li>You're logged in with the custom auth system but not with Supabase</li>
            <li>Your browser cookies are out of sync</li>
          </ul>
          
          <p className="mb-6">
            Click the button below to fix these issues. You'll need to enter your password to re-authenticate.
          </p>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
              <p className="text-red-600">{error}</p>
            </div>
          )}
          
          {success && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
              <p className="text-green-600">{success}</p>
            </div>
          )}
          
          <Button 
            onClick={fixSupabaseAuth} 
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Fixing Authentication...' : 'Fix Authentication Issues'}
          </Button>
        </div>
        
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-2 text-amber-800">Why am I seeing this?</h3>
          <p className="text-amber-700 mb-4">
            Your dashboard is experiencing API errors because of authentication issues. The errors you're seeing 
            (404 Not Found and 406 Not Acceptable) occur when API requests don't have the proper authentication.
          </p>
          <p className="text-amber-700">
            After fixing this issue, you should be able to use all features of the application without errors.
          </p>
        </div>
      </div>
    </MainLayout>
  );
}

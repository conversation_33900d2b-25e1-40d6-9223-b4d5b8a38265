/**
 * Subscription tier limits configuration
 * This file defines the limits for each subscription tier
 */

export type SubscriptionTier = 'free' | 'premium';

export interface TierLimits {
  maxTrustees: number;
  maxAssets: number;
  maxContacts: number;
  maxVaultDocuments: number;
  maxVaultStorageGB: number;
  maxTimeCapsules: number;
  maxServiceSunset: number;
  features: {
    vault: boolean;
    timeCapsule: boolean;
    prioritySupport: boolean;
  };
}

export const SUBSCRIPTION_LIMITS: Record<SubscriptionTier, TierLimits> = {
  free: {
    maxTrustees: 1,
    maxAssets: Infinity,
    maxContacts: Infinity,
    maxVaultDocuments: 1, // Free tier: only 1 document allowed
    maxVaultStorageGB: 5, // Storage limit (not enforced for free tier due to document limit)
    maxTimeCapsules: 0,
    maxServiceSunset: Infinity,
    features: {
      vault: true,
      timeCapsule: false,
      prioritySupport: false,
    },
  },
  premium: {
    maxTrustees: 5,
    maxAssets: Infinity,
    maxContacts: Infinity,
    maxVaultDocuments: Infinity, // Premium tier: unlimited documents
    maxVaultStorageGB: 5, // Premium tier: limited by 5GB total storage
    maxTimeCapsules: 100,
    maxServiceSunset: Infinity,
    features: {
      vault: true,
      timeCapsule: true,
      prioritySupport: true,
    },
  },
};

/**
 * Get the limits for a specific subscription tier
 * @param tier The subscription tier
 * @returns The limits for the specified tier
 */
export function getSubscriptionLimits(tier: SubscriptionTier): TierLimits {
  return SUBSCRIPTION_LIMITS[tier] || SUBSCRIPTION_LIMITS.free;
}

/**
 * Check if a user has reached a specific limit
 * @param tier The user's subscription tier
 * @param limitType The type of limit to check
 * @param currentCount The current count of items
 * @returns Whether the user has reached the limit
 */
export function hasReachedLimit(
  tier: SubscriptionTier,
  limitType: keyof TierLimits,
  currentCount: number
): boolean {
  const limits = getSubscriptionLimits(tier);
  const limit = limits[limitType];

  if (typeof limit === 'number') {
    return currentCount >= limit;
  }

  return false;
}

/**
 * Format a limit value for display
 * @param limit The limit value
 * @returns Formatted limit string
 */
export function formatLimit(limit: number): string {
  if (limit === Infinity) {
    return 'Unlimited';
  }
  return limit.toString();
}
